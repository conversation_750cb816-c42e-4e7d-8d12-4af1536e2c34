import type { Metadata } from "next";
import { DM_Sans } from "next/font/google";
import "./globals.css";
import LenisProvider from "@/providers/lenis";

const dmSans = DM_Sans({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});


export const metadata: Metadata = {
  title: "Infinity Code Tools - AI-powered Shopify Apps & SaaS",
  description: "Landing page for Infinity Code Tools - AI-powered Shopify Apps & Saas apps",
  keywords: ["Shopify Apps", "SaaS", "AI-powered", "E-commerce", "Infinity Code Tools"],
  authors: [{ name: "Infinity Code Tools" }],
  creator: "Infinity Code Tools",
  publisher: "Infinity Code Tools",
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
  openGraph: {
    title: "Infinity Code Tools - AI-powered Shopify Apps & SaaS",
    description: "Landing page for Infinity Code Tools - AI-powered Shopify Apps & Saas apps",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Infinity Code Tools - AI-powered Shopify Apps & SaaS",
    description: "Landing page for Infinity Code Tools - AI-powered Shopify Apps & Saas apps",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${dmSans.className} antialiased w-full min-h-screen overflow-x-hidden`}
      >
        <LenisProvider>
          {children}
        </LenisProvider>
      </body>
    </html>
  );
}

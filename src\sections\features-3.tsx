'use client'

import Badge from "@/components/badge"
import Card from "@/components/card"
import SlideEffect from "@/components/slide-effect"
import Image from "next/image"
import { Play } from "lucide-react"

const settings = {
  badge: {
    number: 1,
    text: 'AI-POWERED SOLUTIONS',
  },
  title: 'Shopify Apps That Work Like Agents',
  description: 'Harness the power of AI agents to automate operations, understand customers, and create seamless shopping experiences.',
  card_1: {
    title: 'AI Audience Insights',
    content: 'Unlock deep customer segmentation using behavior & AI clustering.',
    videoThumbnail: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=225&fit=crop&crop=center'
  },
  card_2: {
    title: 'AI Voice Pilot',
    content: 'Let customers interact with your store via voice commands.',
    videoThumbnail: 'https://images.unsplash.com/photo-1589254065878-42c9da997008?w=400&h=225&fit=crop&crop=center'
  },
  card_3: {
    title: 'AI Store Co-Pilot',
    content: 'Automate store operations like inventory, support & orders.',
    videoThumbnail: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=225&fit=crop&crop=center'
  },
}

export default function Features3() {
  return (
    <div className="space-y-6 sm:space-y-7 md:space-y-8 lg:space-y-10 mx-auto text-center">
      {/* Badge */}
      <SlideEffect>
        <Badge number={settings.badge.number} text={settings.badge.text} />
      </SlideEffect>

      {/* Title */}
      <SlideEffect>
        <h2 className="text-2xl md:text-4xl lg:text-header capitalize text-transparent bg-clip-text bg-gradient-to-b from-black to-black/60 font-medium leading-normal">{settings.title}</h2>
      </SlideEffect>

      {/* Description */}
      <SlideEffect className="px-2 sm:px-10 md:px-0 w-full md:max-w-3/4 mx-auto text-sm lg:text-base">{settings.description}</SlideEffect>

      {/* Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* card 1 */}
        <SlideEffect direction="top" className="col-span-1 h-full" isSpring={false}>
          <Card className="overflow-hidden">
            <div className="relative mb-4 group cursor-pointer">
              <Image
                src={settings.card_1.videoThumbnail}
                alt={settings.card_1.title}
                width={400}
                height={225}
                className="w-full h-48 object-cover rounded-lg transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="bg-white/90 rounded-full p-3">
                  <Play size={24} className="text-black ml-1" />
                </div>
              </div>
            </div>
            <h3 className="text-xl md:text-title text-black font-medium mb-3">{settings.card_1.title}</h3>
            <p className="text-gray-600">{settings.card_1.content}</p>
          </Card>
        </SlideEffect>

        {/* card 2 */}
        <SlideEffect direction="top" delay={0.2} className="col-span-1 h-full" isSpring={false}>
          <Card className="overflow-hidden">
            <div className="relative mb-4 group cursor-pointer">
              <Image
                src={settings.card_2.videoThumbnail}
                alt={settings.card_2.title}
                width={400}
                height={225}
                className="w-full h-48 object-cover rounded-lg transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="bg-white/90 rounded-full p-3">
                  <Play size={24} className="text-black ml-1" />
                </div>
              </div>
            </div>
            <h3 className="text-xl md:text-title text-black font-medium mb-3">{settings.card_2.title}</h3>
            <p className="text-gray-600">{settings.card_2.content}</p>
          </Card>
        </SlideEffect>

        {/* card 3 */}
        <SlideEffect direction="top" delay={0.3} className="col-span-1 h-full" isSpring={false}>
          <Card className="overflow-hidden">
            <div className="relative mb-4 group cursor-pointer">
              <Image
                src={settings.card_3.videoThumbnail}
                alt={settings.card_3.title}
                width={400}
                height={225}
                className="w-full h-48 object-cover rounded-lg transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="bg-white/90 rounded-full p-3">
                  <Play size={24} className="text-black ml-1" />
                </div>
              </div>
            </div>
            <h3 className="text-xl md:text-title text-black font-medium mb-3">{settings.card_3.title}</h3>
            <p className="text-gray-600">{settings.card_3.content}</p>
          </Card>
        </SlideEffect>
      </div>
    </div>
  )
}
'use client'

import Badge from "@/components/badge"
import Card from "@/components/card"
import SlideEffect from "@/components/slide-effect"
import { BarChart3, Mic, Settings } from "lucide-react"

const settings = {
  badge: {
    number: 3,
    text: 'AI-POWERED SOLUTIONS',
  },
  title: 'Transform Your Store with AI',
  description: 'Harness the power of artificial intelligence to automate operations, understand customers, and create seamless shopping experiences.',
  card_1: {
    title: 'AI Audience Insights',
    content: 'Unlock deep customer segmentation using behavior & AI clustering.',
    icon: BarChart3
  },
  card_2: {
    title: 'AI Voice Pilot',
    content: 'Let customers interact with your store via voice commands.',
    icon: Mic
  },
  card_3: {
    title: 'AI Store Co-Pilot',
    content: 'Automate store operations like inventory, support & orders.',
    icon: Settings
  },
}

export default function Features3() {
  return (
    <div className="space-y-6 sm:space-y-7 md:space-y-8 lg:space-y-10 mx-auto text-center">
      {/* Badge */}
      <SlideEffect>
        <Badge number={settings.badge.number} text={settings.badge.text} />
      </SlideEffect>

      {/* Title */}
      <SlideEffect>
        <h2 className="text-2xl md:text-4xl lg:text-header capitalize text-transparent bg-clip-text bg-gradient-to-b from-black to-black/60 font-medium leading-normal">{settings.title}</h2>
      </SlideEffect>

      {/* Description */}
      <SlideEffect className="px-2 sm:px-10 md:px-0 w-full md:max-w-3/4 mx-auto text-sm lg:text-base">{settings.description}</SlideEffect>

      {/* Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* card 1 */}
        <SlideEffect direction="top" className="col-span-1 h-full" isSpring={false}>
          <Card className="text-center">
            <div className="mb-6 flex justify-center">
              <div className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl">
                <settings.card_1.icon size={32} className="text-blue-600" />
              </div>
            </div>
            <h3 className="text-xl md:text-title text-black font-medium mb-3">{settings.card_1.title}</h3>
            <p className="text-gray-600">{settings.card_1.content}</p>
          </Card>
        </SlideEffect>

        {/* card 2 */}
        <SlideEffect direction="top" delay={0.2} className="col-span-1 h-full" isSpring={false}>
          <Card className="text-center">
            <div className="mb-6 flex justify-center">
              <div className="p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl">
                <settings.card_2.icon size={32} className="text-green-600" />
              </div>
            </div>
            <h3 className="text-xl md:text-title text-black font-medium mb-3">{settings.card_2.title}</h3>
            <p className="text-gray-600">{settings.card_2.content}</p>
          </Card>
        </SlideEffect>

        {/* card 3 */}
        <SlideEffect direction="top" delay={0.3} className="col-span-1 h-full" isSpring={false}>
          <Card className="text-center">
            <div className="mb-6 flex justify-center">
              <div className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl">
                <settings.card_3.icon size={32} className="text-purple-600" />
              </div>
            </div>
            <h3 className="text-xl md:text-title text-black font-medium mb-3">{settings.card_3.title}</h3>
            <p className="text-gray-600">{settings.card_3.content}</p>
          </Card>
        </SlideEffect>
      </div>
    </div>
  )
}